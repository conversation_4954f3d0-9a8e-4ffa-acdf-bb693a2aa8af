# 生产环境配置
NODE_ENV = production

# 生产环境平台打包路径
VITE_PUBLIC_PATH = /

# 生产环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "h5"

# 生产环境后端API地址（请根据实际部署环境修改）
VITE_BASE_API = "https://api.yourdomain.com"

# 生产环境代理（生产环境通常不需要代理）
VITE_PROXY_DOMAIN = ""

# 生产环境是否开启 mock
VITE_USE_MOCK = "false"

# 生产环境是否开启包依赖分析可视化
VITE_USE_ANALYZE = "false"

# 是否在打包时使用cdn替换本地库 替换 true 不替换 false
VITE_CDN = false

# 是否启用gzip压缩或brotli压缩（分两种情况，删除原始文件和不删除原始文件）
# 压缩时不删除原始文件的配置：gzip、brotli、both（同时开启 gzip 与 brotli 压缩）、none（不开启压缩，默认）
# 压缩时删除原始文件的配置：gzip-clear、brotli-clear、both-clear（同时开启 gzip 与 brotli 压缩）、none（不开启压缩，默认）
VITE_COMPRESSION = "gzip"

# 生产环境是否隐藏首页
VITE_HIDE_HOME = "false"

# 生产环境是否删除console
VITE_DELETE_CONSOLE = "true"

# 生产环境是否删除debugger
VITE_DELETE_DEBUGGER = "true"

# 生产环境是否开启PWA
VITE_PWA = "false"

# 生产环境是否开启错误监控
VITE_ERROR_MONITORING = "true"

# 错误监控DSN（如使用Sentry等服务）
VITE_SENTRY_DSN = ""

# 应用版本号
VITE_APP_VERSION = "1.0.0"

# 应用标题
VITE_APP_TITLE = "数据库同步管理系统"