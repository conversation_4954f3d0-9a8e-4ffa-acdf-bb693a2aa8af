# 平台本地运行端口号
VITE_PORT = 3000

# 开发环境读取配置文件路径
VITE_PUBLIC_PATH = /

# 开发环境路由历史模式（Hash模式传"hash"、HTML5模式传"h5"、Hash模式带base参数传"hash,base参数"、HTML5模式带base参数传"h5,base参数"）
VITE_ROUTER_HISTORY = "hash"

# 后端API基础地址 (开发环境使用代理，设置为空字符串)
VITE_BASE_API = ""

# 开发环境代理
VITE_PROXY_DOMAIN = "http://localhost:8080"

# 开发环境是否开启 mock
VITE_USE_MOCK = "false"

# 开发环境是否开启包依赖分析可视化
VITE_USE_ANALYZE = "false"

# 开发环境是否开启gzip或brotli压缩
VITE_COMPRESSION = "none"

# 开发环境是否开启CDN
VITE_CDN = "false"
