# application.properties

# ?? MyBatis Plus ?????
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Database connections
db.connections.oracle1.url=*******************************************
db.connections.oracle1.username=PT1_ECI_CQDM
db.connections.oracle1.password=ecidh.com2024
db.connections.oracle1.driverClassName=oracle.jdbc.driver.OracleDriver

db.connections.pg1.url=*****************************************
db.connections.pg1.username=user
db.connections.pg1.password=pass
db.connections.pg1.driverClassName=org.postgresql.Driver

db.connections.dameng1.url=jdbc:dm://localhost:5236/dameng
db.connections.dameng1.username=user
db.connections.dameng1.password=pass
db.connections.dameng1.driverClassName=dm.jdbc.driver.DmDriver

db.connections.vastbase1.url=***************************************************
db.connections.vastbase1.username=cqdm_basic
db.connections.vastbase1.password=cqdm_basic_1qaz
db.connections.vastbase1.driverClassName=org.postgresql.Driver

db.connections.mysql1.url=*********************************
db.connections.mysql1.username=user
db.connections.mysql1.password=pass
db.connections.mysql1.driverClassName=com.mysql.cj.jdbc.Driver

db.connections.sqlserver1.url=******************************************************
db.connections.sqlserver1.username=user
db.connections.sqlserver1.password=pass
db.connections.sqlserver1.driverClassName=com.microsoft.sqlserver.jdbc.SQLServerDriver
db.connections.sqlserver1.dbType=sqlserver

# Example connections for sync tasks
db.connections.oracleHR.url=*************************************
db.connections.oracleHR.username=hr_user
db.connections.oracleHR.password=hr_pass
db.connections.oracleHR.driverClassName=oracle.jdbc.driver.OracleDriver
db.connections.oracleHR.dbType=oracle

db.connections.pgStaging.url=******************************************
db.connections.pgStaging.username=staging_user
db.connections.pgStaging.password=staging_pass
db.connections.pgStaging.driverClassName=org.postgresql.Driver
db.connections.pgStaging.dbType=postgresql

db.connections.mysqlEvents.url=************************************
db.connections.mysqlEvents.username=event_user
db.connections.mysqlEvents.password=event_pass
db.connections.mysqlEvents.driverClassName=com.mysql.cj.jdbc.Driver
db.connections.mysqlEvents.dbType=mysql

db.connections.dwPostgres.url=**********************************************
db.connections.dwPostgres.username=dw_user
db.connections.dwPostgres.password=dw_pass
db.connections.dwPostgres.driverClassName=org.postgresql.Driver
db.connections.dwPostgres.dbType=postgresql


# --- Synchronization Tasks ---
sync.tasks[0].name=OracleHRToPostgresStaging
sync.tasks[0].sourceConnectionId=oracleHR
sync.tasks[0].targetConnectionId=pgStaging
sync.tasks[0].sourceSchemaName=HR
sync.tasks[0].targetSchemaName=staging
sync.tasks[0].tables=EMPLOYEES,DEPARTMENTS
sync.tasks[0].truncateBeforeSync=true

sync.tasks[1].name=MySQLEventsToDataWarehouse
sync.tasks[1].sourceConnectionId=mysqlEvents
sync.tasks[1].targetConnectionId=dwPostgres
# sourceSchemaName might be omitted if MySQL schema is the database name itself (handled by TableMetadataSqlProvider if schemaName is null)
sync.tasks[1].targetSchemaName=event_archive
sync.tasks[1].tables=EVENT_LOG,USER_ACTIONS
sync.tasks[1].truncateBeforeSync=false


# --- Test DB Connections & Tasks for Integration Testing ---
# These can point to the same actual PostgreSQL instance for simplicity in this environment.
# Schema/table names should be chosen to avoid conflict with real data.
db.connections.pgTestSource.url=*****************************************
db.connections.pgTestSource.username=user
db.connections.pgTestSource.password=pass
db.connections.pgTestSource.driverClassName=org.postgresql.Driver
db.connections.pgTestSource.dbType=postgresql

db.connections.pgTestTarget.url=*****************************************
db.connections.pgTestTarget.username=user
db.connections.pgTestTarget.password=pass
db.connections.pgTestTarget.driverClassName=org.postgresql.Driver
db.connections.pgTestTarget.dbType=postgresql

sync.tasks[2].name=TestPgToPgTableCopy
sync.tasks[2].sourceConnectionId=pgTestSource
sync.tasks[2].targetConnectionId=pgTestTarget
sync.tasks[2].sourceSchemaName=public # Assuming 'public' schema for test
sync.tasks[2].targetSchemaName=public # Assuming 'public' schema for test
sync.tasks[2].tables=dbsync_test_source_table # Table to be created manually for testing
sync.tasks[2].truncateBeforeSync=true

# ?? Bean ??
spring.main.allow-bean-definition-overriding=true

# ?????? (Oracle)
spring.datasource.oracle.jdbc-url=*******************************************
spring.datasource.oracle.username=PT1_ECI_CQDM
spring.datasource.oracle.password=ecidh.com2024
spring.datasource.oracle.driver-class-name=oracle.jdbc.driver.OracleDriver

# ??????? (PostgreSQL)
spring.datasource.postgres.jdbc-url=***************************************************
spring.datasource.postgres.username=cqdm_basic
spring.datasource.postgres.password=cqdm_basic_1qaz
spring.datasource.postgres.driver-class-name=org.postgresql.Driver

# MyBatis ??
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# ????
logging.level.root=INFO
logging.level.com.dbsync.dbsync=DEBUG
logging.level.com.dbsync.dbsync.mapper=DEBUG

dbsync.truncate-before-sync=true
dbsync.source.db-type=oracle
dbsync.target.db-type=postgres
dbsync.target.schema-name=public

# SQLite数据源配置（用于用户认证）
# 数据源配置在AuthDataSourceConfig中定义

# JWT配置
jwt.secret=mySecretKey123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890
jwt.expiration=86400000