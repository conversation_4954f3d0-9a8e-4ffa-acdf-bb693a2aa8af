package com.dbsync.dbsync.integration;

import com.dbsync.dbsync.service.DatabaseSyncService;
import com.dbsync.dbsync.service.progress.ProgressManager;
import com.dbsync.dbsync.service.type.TypeMappingRegistry;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfEnvironmentVariable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for PostgreSQL cluster synchronization
 * This test requires actual PostgreSQL cluster setup and should only run in appropriate environments
 */
@SpringBootTest
@ActiveProfiles("cluster-test")
@TestPropertySource(locations = "classpath:application-cluster-test.properties")
@EnabledIfEnvironmentVariable(named = "ENABLE_CLUSTER_TESTS", matches = "true")
public class PostgreSQLClusterIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(PostgreSQLClusterIntegrationTest.class);

    @Autowired
    @Qualifier("oracleSqlSessionFactory")
    private SqlSessionFactory sourceFactory;

    @Autowired
    @Qualifier("postgresSqlSessionFactory")
    private SqlSessionFactory targetFactory;

    @Autowired
    private TypeMappingRegistry typeMappingRegistry;

    @Autowired
    private ProgressManager progressManager;

    @Test
    void testClusterSynchronization() {
        logger.info("Starting PostgreSQL cluster synchronization test");

        // Create DatabaseSyncService with cluster-aware configuration
        DatabaseSyncService syncService = new DatabaseSyncService(
            sourceFactory,
            targetFactory,
            true, // truncateBeforeSync
            typeMappingRegistry,
            "oracle", // sourceDbType
            "postgresql", // targetDbType
            "public", // targetSchemaName
            progressManager
        );

        String taskId = "cluster-test-" + System.currentTimeMillis();
        List<String> tablesToSync = Arrays.asList("BMC_BD_FIX_SOURCE");
        String sourceSchemaName = "PT1_ECI_CQDM";

        // Execute synchronization
        assertDoesNotThrow(() -> {
            syncService.syncDatabase(taskId, tablesToSync, sourceSchemaName);
        }, "Cluster synchronization should complete without errors");

        logger.info("PostgreSQL cluster synchronization test completed successfully");
    }

    @Test
    void testTableCreationWithClusterReplication() {
        logger.info("Testing table creation with cluster replication");

        DatabaseSyncService syncService = new DatabaseSyncService(
            sourceFactory,
            targetFactory,
            true,
            typeMappingRegistry,
            "oracle",
            "postgresql",
            "public",
            progressManager
        );

        String taskId = "cluster-table-test-" + System.currentTimeMillis();
        List<String> tablesToSync = Arrays.asList("TEST_CLUSTER_TABLE");
        String sourceSchemaName = "PT1_ECI_CQDM";

        // This test focuses on table creation and replication validation
        assertDoesNotThrow(() -> {
            syncService.syncDatabase(taskId, tablesToSync, sourceSchemaName);
        }, "Table creation with cluster replication should succeed");

        logger.info("Table creation with cluster replication test completed");
    }

    @Test
    void testBatchInsertWithClusterValidation() {
        logger.info("Testing batch insert with cluster validation");

        DatabaseSyncService syncService = new DatabaseSyncService(
            sourceFactory,
            targetFactory,
            false, // Don't truncate, assume table exists
            typeMappingRegistry,
            "oracle",
            "postgresql",
            "public",
            progressManager
        );

        String taskId = "cluster-insert-test-" + System.currentTimeMillis();
        List<String> tablesToSync = Arrays.asList("BMC_BD_FIX_SOURCE");
        String sourceSchemaName = "PT1_ECI_CQDM";

        // This test focuses on data insertion with cluster awareness
        assertDoesNotThrow(() -> {
            syncService.syncDatabase(taskId, tablesToSync, sourceSchemaName);
        }, "Batch insert with cluster validation should succeed");

        logger.info("Batch insert with cluster validation test completed");
    }
}

/**
 * Manual test runner for PostgreSQL cluster testing
 * Run this when you have access to the actual cluster environment
 */
class ManualClusterTestRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(ManualClusterTestRunner.class);
    
    public static void main(String[] args) {
        logger.info("=== Manual PostgreSQL Cluster Test Runner ===");
        logger.info("This test runner is designed to be executed manually when:");
        logger.info("1. PostgreSQL cluster is available at 192.168.106.103:5432");
        logger.info("2. Oracle source is available at 192.168.107.101:1525");
        logger.info("3. Test data exists in the source database");
        logger.info("");
        logger.info("To run the integration tests:");
        logger.info("1. Set environment variable: ENABLE_CLUSTER_TESTS=true");
        logger.info("2. Ensure database connections are accessible");
        logger.info("3. Run: mvn test -Dtest=PostgreSQLClusterIntegrationTest");
        logger.info("");
        logger.info("Expected behavior after fixes:");
        logger.info("- Table creation should wait for cluster replication");
        logger.info("- Column filtering should preserve all valid columns");
        logger.info("- Error messages should provide cluster-specific diagnostics");
        logger.info("- DDL operations should use proper transaction management");
        logger.info("- Batch inserts should validate table existence before execution");
        logger.info("=== End of Manual Test Instructions ===");
    }
}
