package com.dbsync.dbsync.service;

import com.dbsync.dbsync.mapper.auth.DbConnectionMapper;
import com.dbsync.dbsync.model.DbConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据库连接管理服务
 */
@Service
public class DbConnectionService {

    @Autowired
    private DbConnectionMapper dbConnectionMapper;

    /**
     * 获取所有数据库连接
     */
    public List<DbConnection> getAllConnections() {
        return dbConnectionMapper.findAllConnections();
    }

    /**
     * 获取所有启用的数据库连接
     */
    public List<DbConnection> getEnabledConnections() {
        return dbConnectionMapper.findAllEnabled();
    }

    /**
     * 根据ID获取数据库连接
     */
    public DbConnection getConnectionById(Long id) {
        return dbConnectionMapper.findById(id);
    }

    /**
     * 根据名称获取数据库连接
     */
    public DbConnection getConnectionByName(String name) {
        return dbConnectionMapper.findByName(name);
    }

    /**
     * 创建数据库连接
     */
    @Transactional
    public DbConnection createConnection(DbConnection connection) {
        // 检查名称是否已存在
        if (dbConnectionMapper.existsByName(connection.getName())) {
            throw new RuntimeException("连接名称已存在: " + connection.getName());
        }
        
        // 设置创建时间和更新时间
        String now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        connection.setCreatedAt(now);
        connection.setUpdatedAt(now);
        
        // 插入数据库
        int result = dbConnectionMapper.insertConnection(connection);
        if (result > 0) {
            return connection;
        } else {
            throw new RuntimeException("创建数据库连接失败");
        }
    }

    /**
     * 更新数据库连接
     */
    @Transactional
    public DbConnection updateConnection(Long id, DbConnection connection) {
        // 检查连接是否存在
        DbConnection existingConnection = dbConnectionMapper.findById(id);
        if (existingConnection == null) {
            throw new RuntimeException("数据库连接不存在: " + id);
        }
        
        // 检查名称是否已被其他连接使用
        if (dbConnectionMapper.existsByNameExcludingId(connection.getName(), id)) {
            throw new RuntimeException("连接名称已存在: " + connection.getName());
        }
        
        // 设置ID和更新时间
        connection.setId(id);
        connection.setUpdatedAt(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        
        // 更新数据库
        int result = dbConnectionMapper.updateConnection(connection);
        if (result > 0) {
            return connection;
        } else {
            throw new RuntimeException("更新数据库连接失败");
        }
    }

    /**
     * 删除数据库连接
     */
    @Transactional
    public boolean deleteConnection(Long id) {
        // 检查连接是否存在
        DbConnection existingConnection = dbConnectionMapper.findById(id);
        if (existingConnection == null) {
            throw new RuntimeException("数据库连接不存在: " + id);
        }
        
        int result = dbConnectionMapper.deleteById(id);
        return result > 0;
    }

    /**
     * 启用/禁用数据库连接
     */
    @Transactional
    public boolean toggleConnectionStatus(Long id, Boolean enabled) {
        // 检查连接是否存在
        DbConnection existingConnection = dbConnectionMapper.findById(id);
        if (existingConnection == null) {
            throw new RuntimeException("数据库连接不存在: " + id);
        }
        
        int result = dbConnectionMapper.updateConnectionStatus(id, enabled);
        return result > 0;
    }

    /**
     * 测试数据库连接
     */
    public DbTestResult testConnection(DbConnection connection) {
        String url = buildJdbcUrl(connection);
        long startTime = System.currentTimeMillis();
        
        try (Connection conn = DriverManager.getConnection(url, connection.getUsername(), connection.getPassword())) {
            long connectionTime = System.currentTimeMillis() - startTime;
            
            // 测试连接是否有效
            if (conn.isValid(5)) {
                return new DbTestResult(true, "连接测试成功", connectionTime);
            } else {
                return new DbTestResult(false, "连接无效", connectionTime);
            }
        } catch (SQLException e) {
            long connectionTime = System.currentTimeMillis() - startTime;
            return new DbTestResult(false, "连接失败: " + e.getMessage(), connectionTime);
        }
    }

    /**
     * 获取数据库连接的表列表
     */
    public List<String> getTables(Long connectionId, String schemaName) {
        // 获取数据库连接
        DbConnection connection = getConnectionById(connectionId);
        if (connection == null) {
            throw new RuntimeException("数据库连接不存在: " + connectionId);
        }
        
        String url = buildJdbcUrl(connection);
        List<String> tables = new ArrayList<>();
        
        try (Connection conn = DriverManager.getConnection(url, connection.getUsername(), connection.getPassword())) {
            // 根据数据库类型构建SQL
            String sql = getTablesSql(connection.getDbType(), schemaName);
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                // 设置参数
                if (sql.contains("?")) {
                    if (schemaName != null && !schemaName.trim().isEmpty()) {
                        stmt.setString(1, schemaName);
                    } else {
                        stmt.setString(1, getDefaultSchema(connection.getDbType(), connection.getDatabase()));
                    }
                }
                
                // 执行查询
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        String tableName = rs.getString(1);
                        if (tableName != null && !tableName.trim().isEmpty()) {
                            tables.add(tableName);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            throw new RuntimeException("获取表列表失败: " + e.getMessage(), e);
        }
        
        return tables;
    }

    /**
     * 根据数据库类型获取表列表的SQL
     */
    private String getTablesSql(String dbType, String schemaName) {
        switch (dbType.toLowerCase()) {
            case "mysql":
                if (schemaName != null && !schemaName.trim().isEmpty()) {
                    return "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_type = 'BASE TABLE' ORDER BY table_name";
                } else {
                    return "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_type = 'BASE TABLE' ORDER BY table_name";
                }
            case "postgresql":
            case "vastbase":
                if (schemaName != null && !schemaName.trim().isEmpty()) {
                    return "SELECT tablename FROM pg_tables WHERE schemaname = ? ORDER BY tablename";
                } else {
                    return "SELECT tablename FROM pg_tables WHERE schemaname = 'public' ORDER BY tablename";
                }
            case "oracle":
                if (schemaName != null && !schemaName.trim().isEmpty()) {
                    return "SELECT table_name FROM all_tables WHERE owner = UPPER(?) ORDER BY table_name";
                } else {
                    return "SELECT table_name FROM user_tables ORDER BY table_name";
                }
            case "sqlserver":
                if (schemaName != null && !schemaName.trim().isEmpty()) {
                    return "SELECT table_name FROM information_schema.tables WHERE table_schema = ? AND table_type = 'BASE TABLE' ORDER BY table_name";
                } else {
                    return "SELECT table_name FROM information_schema.tables WHERE table_schema = 'dbo' AND table_type = 'BASE TABLE' ORDER BY table_name";
                }
            case "dameng":
                if (schemaName != null && !schemaName.trim().isEmpty()) {
                    return "SELECT table_name FROM all_tables WHERE owner = UPPER(?) ORDER BY table_name";
                } else {
                    return "SELECT table_name FROM user_tables ORDER BY table_name";
                }
            default:
                throw new RuntimeException("不支持的数据库类型: " + dbType);
        }
    }

    /**
     * 获取默认的schema
     */
    private String getDefaultSchema(String dbType, String database) {
        switch (dbType.toLowerCase()) {
            case "mysql":
                return database;
            case "postgresql":
            case "vastbase":
                return "public";
            case "oracle":
            case "dameng":
                return database.toUpperCase();
            case "sqlserver":
                return "dbo";
            default:
                return database;
        }
    }

    /**
     * 构建JDBC URL
     */
    private String buildJdbcUrl(DbConnection connection) {
        String dbType = connection.getDbType();
        String host = connection.getHost();
        Integer port = connection.getPort();
        String database = connection.getDatabase();
        String schema = connection.getSchema();
        
        switch (dbType.toLowerCase()) {
            case "mysql":
                return String.format("*****************************************************", host, port, database);
            case "postgresql":
            case "vastbase":
                return String.format("jdbc:postgresql://%s:%d/%s", host, port, database);
            case "oracle":
                return String.format("****************************", host, port, database);
            case "sqlserver":
                return String.format("**************************************", host, port, database);
            case "dameng":
                return String.format("jdbc:dm://%s:%d/%s", host, port, database);
            default:
                throw new RuntimeException("不支持的数据库类型: " + dbType);
        }
    }

    /**
     * 数据库连接测试结果
     */
    public static class DbTestResult {
        private boolean success;
        private String message;
        private long connectionTime;

        public DbTestResult(boolean success, String message, long connectionTime) {
            this.success = success;
            this.message = message;
            this.connectionTime = connectionTime;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public long getConnectionTime() {
            return connectionTime;
        }

        public void setConnectionTime(long connectionTime) {
            this.connectionTime = connectionTime;
        }
    }
}