package com.dbsync.dbsync.service;

import com.dbsync.dbsync.mapper.TableMapper;
import com.dbsync.dbsync.service.progress.ProgressManager;
import com.dbsync.dbsync.service.type.TypeMappingRegistry;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for PostgreSQL cluster synchronization fixes
 * Tests the fixes for table creation and data insertion synchronization issues
 */
@ExtendWith(MockitoExtension.class)
public class PostgreSQLClusterSyncTest {

    private static final Logger logger = LoggerFactory.getLogger(PostgreSQLClusterSyncTest.class);

    @Mock
    private SqlSessionFactory sourceFactory;
    
    @Mock
    private SqlSessionFactory targetFactory;
    
    @Mock
    private SqlSession sourceSession;
    
    @Mock
    private SqlSession targetSession;
    
    @Mock
    private TableMapper sourceMapper;
    
    @Mock
    private TableMapper targetMapper;
    
    @Mock
    private TypeMappingRegistry typeMappingRegistry;
    
    @Mock
    private ProgressManager progressManager;

    private DatabaseSyncService syncService;

    @BeforeEach
    void setUp() {
        // Setup mock behavior
        when(sourceFactory.openSession()).thenReturn(sourceSession);
        when(targetFactory.openSession()).thenReturn(targetSession);
        when(sourceSession.getMapper(TableMapper.class)).thenReturn(sourceMapper);
        when(targetSession.getMapper(TableMapper.class)).thenReturn(targetMapper);

        // Create service instance with PostgreSQL target
        syncService = new DatabaseSyncService(
            sourceFactory,
            targetFactory,
            true, // truncateBeforeSync
            typeMappingRegistry,
            "oracle", // sourceDbType
            "postgresql", // targetDbType
            "public", // targetSchemaName
            progressManager
        );
    }

    @Test
    void testTableReplicationWait_Success() throws Exception {
        // Test successful table replication detection
        String taskId = "test-task-1";
        String tableName = "test_table";
        
        // Mock cluster-aware table existence check
        Map<String, Object> tableInfo = new HashMap<>();
        tableInfo.put("table_count", 1L);
        tableInfo.put("stats_count", 1L);
        
        when(targetMapper.checkPgTableExistsClusterAware(eq(tableName), eq("public")))
            .thenReturn(tableInfo);

        // Use reflection to test private method
        java.lang.reflect.Method method = DatabaseSyncService.class
            .getDeclaredMethod("waitForTableReplication", String.class, SqlSession.class, String.class);
        method.setAccessible(true);

        // Should not throw exception
        assertDoesNotThrow(() -> {
            method.invoke(syncService, taskId, targetSession, tableName);
        });

        // Verify the method was called
        verify(targetMapper, atLeastOnce()).checkPgTableExistsClusterAware(eq(tableName), eq("public"));
    }

    @Test
    void testTableReplicationWait_Retry() throws Exception {
        // Test retry mechanism when table is not immediately available
        String taskId = "test-task-2";
        String tableName = "test_table";
        
        // Mock initial failure then success
        Map<String, Object> failureInfo = new HashMap<>();
        failureInfo.put("table_count", 0L);
        failureInfo.put("stats_count", 0L);
        
        Map<String, Object> successInfo = new HashMap<>();
        successInfo.put("table_count", 1L);
        successInfo.put("stats_count", 1L);
        
        when(targetMapper.checkPgTableExistsClusterAware(eq(tableName), eq("public")))
            .thenReturn(failureInfo)  // First call fails
            .thenReturn(successInfo); // Second call succeeds

        // Use reflection to test private method
        java.lang.reflect.Method method = DatabaseSyncService.class
            .getDeclaredMethod("waitForTableReplication", String.class, SqlSession.class, String.class);
        method.setAccessible(true);

        // Should not throw exception and should retry
        assertDoesNotThrow(() -> {
            method.invoke(syncService, taskId, targetSession, tableName);
        });

        // Verify the method was called multiple times
        verify(targetMapper, atLeast(2)).checkPgTableExistsClusterAware(eq(tableName), eq("public"));
    }

    @Test
    void testPaginationColumnFiltering() throws Exception {
        // Test that pagination columns are properly filtered
        java.lang.reflect.Method method = DatabaseSyncService.class
            .getDeclaredMethod("isPaginationColumn", String.class);
        method.setAccessible(true);

        // Test known pagination columns
        assertTrue((Boolean) method.invoke(syncService, "rnum"));
        assertTrue((Boolean) method.invoke(syncService, "ROWNUM"));
        assertTrue((Boolean) method.invoke(syncService, "rnum_"));
        assertTrue((Boolean) method.invoke(syncService, "rn"));
        assertTrue((Boolean) method.invoke(syncService, "row_number"));
        assertTrue((Boolean) method.invoke(syncService, "__system_col"));
        assertTrue((Boolean) method.invoke(syncService, "custom_rownum"));

        // Test valid data columns
        assertFalse((Boolean) method.invoke(syncService, "id"));
        assertFalse((Boolean) method.invoke(syncService, "name"));
        assertFalse((Boolean) method.invoke(syncService, "datasource_table"));
        assertFalse((Boolean) method.invoke(syncService, "status"));
        assertFalse((Boolean) method.invoke(syncService, null));
    }

    @Test
    void testPostgreSQLClusterErrorDetection() throws Exception {
        // Test PostgreSQL cluster error detection
        java.lang.reflect.Method method = DatabaseSyncService.class
            .getDeclaredMethod("isPostgreSQLClusterError", String.class);
        method.setAccessible(true);

        // Test cluster-related error messages
        assertTrue((Boolean) method.invoke(syncService, "ERROR: relation \"test_table\" does not exist on node1"));
        assertTrue((Boolean) method.invoke(syncService, "connection refused to cluster node"));
        assertTrue((Boolean) method.invoke(syncService, "replication lag detected"));
        assertTrue((Boolean) method.invoke(syncService, "server closed the connection unexpectedly"));

        // Test non-cluster errors
        assertFalse((Boolean) method.invoke(syncService, "syntax error in SQL"));
        assertFalse((Boolean) method.invoke(syncService, "permission denied"));
        assertFalse((Boolean) method.invoke(syncService, null));
    }

    @Test
    void testDDLTransactionManagement() throws Exception {
        // Test DDL transaction management
        String taskId = "test-task-3";
        String tableName = "test_table";
        List<Map<String, Object>> sourceStructure = createMockTableStructure();
        String tableComment = "Test table";
        List<Map<String, String>> columnComments = createMockColumnComments();
        String targetTableName = "test_table";

        // Mock successful DDL execution
        doNothing().when(targetMapper).executeDDL(anyString());
        doNothing().when(targetSession).commit();

        // Mock table replication check
        Map<String, Object> tableInfo = new HashMap<>();
        tableInfo.put("table_count", 1L);
        tableInfo.put("stats_count", 1L);
        when(targetMapper.checkPgTableExistsClusterAware(eq(tableName), eq("public")))
            .thenReturn(tableInfo);

        // Use reflection to test private method
        java.lang.reflect.Method method = DatabaseSyncService.class
            .getDeclaredMethod("executeDDLWithClusterSupport", String.class, SqlSession.class, 
                String.class, List.class, String.class, List.class, String.class);
        method.setAccessible(true);

        // Should execute without exception
        assertDoesNotThrow(() -> {
            method.invoke(syncService, taskId, targetSession, tableName, sourceStructure, 
                tableComment, columnComments, targetTableName);
        });

        // Verify DDL operations were executed
        verify(targetMapper, atLeastOnce()).executeDDL(anyString());
        verify(targetSession, times(1)).commit();
    }

    private List<Map<String, Object>> createMockTableStructure() {
        List<Map<String, Object>> structure = new ArrayList<>();
        
        Map<String, Object> column1 = new HashMap<>();
        column1.put("COLUMN_NAME", "id");
        column1.put("DATA_TYPE", "NUMBER");
        column1.put("DATA_LENGTH", 10);
        column1.put("NULLABLE", "N");
        structure.add(column1);
        
        Map<String, Object> column2 = new HashMap<>();
        column2.put("COLUMN_NAME", "name");
        column2.put("DATA_TYPE", "VARCHAR2");
        column2.put("DATA_LENGTH", 100);
        column2.put("NULLABLE", "Y");
        structure.add(column2);
        
        return structure;
    }

    private List<Map<String, String>> createMockColumnComments() {
        List<Map<String, String>> comments = new ArrayList<>();
        
        Map<String, String> comment1 = new HashMap<>();
        comment1.put("COLUMN_NAME", "id");
        comment1.put("COMMENTS", "Primary key");
        comments.add(comment1);
        
        Map<String, String> comment2 = new HashMap<>();
        comment2.put("COLUMN_NAME", "name");
        comment2.put("COMMENTS", "Name field");
        comments.add(comment2);
        
        return comments;
    }
}
