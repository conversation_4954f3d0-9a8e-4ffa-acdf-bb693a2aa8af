package com.dbsync.dbsync.mapper;

import com.dbsync.dbsync.service.BatchInsertSqlProvider;
import org.apache.ibatis.annotations.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page; // 导入 Page 类

import java.util.List;
import java.util.Map;

@Mapper
public interface TableMapper {

    /**
     * 执行动态 SQL
     * @param sql
     * @param params
     */
    @Insert("${sql}")
    void executeDynamicSQL(
            @Param("sql") String sql,
            @Param("params") Object[] params  // 显式命名参数数组
    );


    @InsertProvider(type = BatchInsertSqlProvider.class, method = "generateBatchInsertSql")
    void executeBatchInsert(@Param("tableName") String tableName, @Param("data") List<Map<String, Object>> data);

    /**
     * Database-agnostic pagination.
     * Parameters 'current', 'size', 'tableName', 'dbType', 'schemaName' (optional), 'orderByColumn' (optional for SQLServer)
     * must be passed in a Map due to provider method signature limitations with multiple params.
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "getTableDataWithPagination")
    List<Map<String, Object>> getTableDataWithPagination(Map<String, Object> params);

    /**
     * 添加执行 DDL 的方法
     * @param sql
     */
    @Update("${sql}")
    void executeDDL(String sql);

    /**
     * 添加执行 DML 的方法
     * @param sql
     */
    @Update("${sql}")
    void executeDML(String sql);

    /**
     * 获取所有表的注释
     * @return
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "getAllTableComments")
    List<Map<String, String>> getAllTableComments(@Param("dbType") String dbType, @Param("schemaName") String schemaName);

    /**
     * 获取指定表的列注释
     * @param dbType Database type (e.g., "oracle", "postgresql")
     * @param tableName Name of the table
     * @param schemaName Optional schema name; may be null
     * @return List of column comments
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "getColumnComments")
    List<Map<String, String>> getColumnComments(@Param("dbType") String dbType, @Param("tableName") String tableName, @Param("schemaName") String schemaName);

    /**
     * 获取指定表的结构信息
     * @param dbType Database type
     * @param tableName Name of the table
     * @param schemaName Optional schema name
     * @return List of column structures
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "getTableStructure")
    List<Map<String, Object>> getTableStructure(@Param("dbType") String dbType, @Param("tableName") String tableName, @Param("schemaName") String schemaName);

    /**
     * 获取指定表的数据
     * @param tableName
     * @return
     */
    @Select("SELECT * FROM ${tableName}")
    List<Map<String, Object>> getTableData(String tableName);

    /**
     * 清空表数据 - This is generally cross-database, but TRUNCATE might need specific permissions.
     * Using ${tableName} is fine as it's controlled internally.
     * @param tableName
     */
    @Update("TRUNCATE TABLE ${tableName}")
    void truncateTable(String tableName);

    /**
     * 获取表的总记录数
     * @param dbType Database type
     * @param tableName Name of the table
     * @param schemaName Optional schema name
     * @return Total number of rows
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "getTableCount")
    long getTableCount(@Param("dbType") String dbType, @Param("tableName") String tableName, @Param("schemaName") String schemaName);

    /**
     * 检查 PostgreSQL 表是否存在
     * @param tableName 要检查的表名
     * @return 如果表存在返回 1，否则返回 0
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "checkPgTableExists")
    long checkPgTableExists(@Param("tableName") String tableName);

    /**
     * 检查 PostgreSQL 表是否存在（集群感知版本）
     * @param tableName 要检查的表名
     * @param schemaName 模式名称
     * @return 包含表存在信息和统计信息的 Map
     */
    @SelectProvider(type = TableMetadataSqlProvider.class, method = "checkPgTableExistsClusterAware")
    Map<String, Object> checkPgTableExistsClusterAware(@Param("tableName") String tableName, @Param("schemaName") String schemaName);
}                 :key="conn.id"
                  :label="conn.name"
                  :value="conn.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标数据库" prop="targetConnectionId">
              <el-select 
                v-model="formData.targetConnectionId" 
                placeholder="请选择目标数据库"
              >
                <el-option
                  v-for="conn in connectionList"
                  :key="conn.id"
                  :label="conn.name"
                  :value="conn.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="源Schema" prop="sourceSchemaName">
              <el-input v-model="formData.sourceSchemaName" placeholder="可选，默认为空" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标Schema" prop="targetSchemaName">
              <el-input v-model="formData.targetSchemaName" placeholder="可选，默认为空" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="同步表" prop="tables">
          <div class="w-full">
            <div class="flex items-center mb-2">
              <el-button size="small" @click="loadSourceTables" :loading="loadingTables">
                加载源表列表
              </el-button>
              <span class="ml-2 text-sm text-gray-500">
                已选择 {{ formData.tables.length }} 个表
              </span>
            </div>
            <el-transfer
              v-model="formData.tables"
              :data="sourceTableList"
              :titles="['可用表', '同步表']"
              filterable
              filter-placeholder="搜索表名"
            />
          </div>
        </el-form-item>

        <el-form-item label="同步选项">
          <el-checkbox v-model="formData.truncateBeforeSync">
            同步前清空目标表数据
          </el-checkbox>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 日志查看对话框 -->
    <el-dialog
      v-model="logDialogVisible"
      title="任务执行日志"
      width="800px"
    >
      <div class="log-container">
        <el-scrollbar height="400px">
          <pre class="log-content">{{ taskLogs.join('\n') }}</pre>
        </el-scrollbar>
      </div>
      <template #footer>
        <el-button @click="logDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="refreshLogs">刷新日志</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import {
  type SyncTask,
  type DbConfig,
  getSyncTasksApi,
  createSyncTaskApi,
  updateSyncTaskApi,
  deleteSyncTaskApi,
  executeSyncTaskApi,
  stopSyncTaskApi,
  getSyncTaskProgressApi,
  getSyncTaskLogsApi,
  getDbConnectionsApi,
  getDbTablesApi
} from "@/api/database";

defineOptions({
  name: "DatabaseSync"
});

// 响应式数据
const loading = ref(false);
const dialogVisible = ref(false);
const logDialogVisible = ref(false);
const isEdit = ref(false);
const submitLoading = ref(false);
const loadingTables = ref(false);
const taskList = ref<SyncTask[]>([]);
const connectionList = ref<DbConfig[]>([]);
const sourceTableList = ref<{key: string, label: string}[]>([]);
const taskLogs = ref<string[]>([]);
const currentLogTaskId = ref<string>("");
const formRef = ref<FormInstance>();
const progressTimer = ref<NodeJS.Timeout>();

// 表单数据
const formData = reactive<SyncTask>({
  name: "",
  sourceConnectionId: "",
  targetConnectionId: "",
  sourceSchemaName: "",
  targetSchemaName: "",
  tables: [],
  truncateBeforeSync: false,
  status: 'PENDING'
});

// 表单验证规则
const formRules: FormRules = {
  name: [{ required: true, message: "请输入任务名称", trigger: "blur" }],
  sourceConnectionId: [{ required: true, message: "请选择源数据库", trigger: "change" }],
  targetConnectionId: [{ required: true, message: "请选择目标数据库", trigger: "change" }],
  tables: [{ required: true, message: "请选择要同步的表", trigger: "change" }]
};

// 获取状态标签样式
const getStatusTagType = (status: string) => {
  const statusMap = {
    'PENDING': '',
    'RUNNING': 'warning',
    'COMPLETED_SUCCESS': 'success',
    'COMPLETED_WITH_ERRORS': 'warning',
    'FAILED': 'danger'
  };
  return statusMap[status] || '';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    'PENDING': '待执行',
    'RUNNING': '执行中',
    'COMPLETED_SUCCESS': '成功',
    'COMPLETED_WITH_ERRORS': '部分成功',
    'FAILED': '失败'
  };
  return statusMap[status] || status;
};

// 获取连接名称
const getConnectionName = (connectionId: string) => {
  const connection = connectionList.value.find(c => c.id === connectionId);
  return connection ? connection.name : connectionId;
};

// 格式化时间
const formatTime = (timeStr: string) => {
  return new Date(timeStr).toLocaleString();
};

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true;
  try {
    taskList.value = await getSyncTasksApi();
  } catch (error) {
    ElMessage.error("获取任务列表失败");
  } finally {
    loading.value = false;
  }
};

// 获取连接列表
const fetchConnections = async () => {
  try {
    connectionList.value = await getDbConnectionsApi();
  } catch (error) {
    ElMessage.error("获取连接列表失败");
  }
};

// 加载源表列表
const loadSourceTables = async () => {
  if (!formData.sourceConnectionId) {
    ElMessage.warning("请先选择源数据库");
    return;
  }
  
  loadingTables.value = true;
  try {
    const tables = await getDbTablesApi(formData.sourceConnectionId.toString(), formData.sourceSchemaName);
    sourceTableList.value = tables.map(table => ({
      key: table,
      label: table
    }));
    ElMessage.success("表列表加载成功");
  } catch (error) {
    ElMessage.error("加载表列表失败");
  } finally {
    loadingTables.value = false;
  }
};

// 源数据库改变时清空表选择
const handleSourceChange = () => {
  formData.tables = [];
  sourceTableList.value = [];
};

// 新增任务
const handleAdd = () => {
  isEdit.value = false;
  resetForm();
  dialogVisible.value = true;
};

// 编辑任务
const handleEdit = (row: SyncTask) => {
  isEdit.value = true;
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 删除任务
const handleDelete = async (row: SyncTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${row.name}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await deleteSyncTaskApi(row.id!.toString());
    ElMessage.success("删除成功");
    fetchTasks();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.error || "删除失败");
    }
  }
};

// 执行任务
const handleExecute = async (row: SyncTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要执行任务 "${row.name}" 吗？`,
      "确认执行",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }
    );
    
    await executeSyncTaskApi(row.id!.toString());
    ElMessage.success("任务已开始执行");
    
    // 更新任务状态为执行中
    row.status = 'RUNNING';
    row.progress = 0;
    
    // 开始轮询进度
    startProgressPolling();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.error || "执行失败");
    }
  }
};

// 停止任务
const handleStop = async (row: SyncTask) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止任务 "${row.name}" 吗？`,
      "确认停止",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    await stopSyncTaskApi(row.id!.toString());
    ElMessage.success("任务已停止");
    row.status = 'FAILED';
    stopProgressPolling();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.error || "停止失败");
    }
  }
};

// 查看日志
const handleViewLogs = async (row: SyncTask) => {
  currentLogTaskId.value = row.id!;
  await refreshLogs();
  logDialogVisible.value = true;
};

// 刷新日志
const refreshLogs = async () => {
  try {
    taskLogs.value = await getSyncTaskLogsApi(currentLogTaskId.value);
  } catch (error) {
    ElMessage.error("获取日志失败");
  }
};

// 开始进度轮询
const startProgressPolling = () => {
  progressTimer.value = setInterval(async () => {
    const runningTasks = taskList.value.filter(t => t.status === 'RUNNING');
    
    if (runningTasks.length === 0) {
      stopProgressPolling();
      return;
    }
    
    // 获取每个运行中任务的真实进度
    for (const task of runningTasks) {
      try {
        const progress = await getSyncTaskProgressApi(task.id!.toString());
        // 更新任务进度
        task.progress = progress.progress;
        task.completedTables = progress.completedTables;
        task.totalTables = progress.totalTables;
        task.status = progress.status;
        
        // 如果任务已完成或失败，停止轮询
        if (task.status === 'COMPLETED_SUCCESS' || task.status === 'FAILED') {
          stopProgressPolling();
          break;
        }
      } catch (error) {
        console.error('获取进度失败：', error);
      }
    }
  }, 2000);
};

// 停止进度轮询
const stopProgressPolling = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value);
    progressTimer.value = undefined;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    submitLoading.value = true;
    
    if (isEdit.value) {
      await updateSyncTaskApi(formData.id!.toString(), formData);
      ElMessage.success("更新成功");
    } else {
      await createSyncTaskApi(formData);
      ElMessage.success("创建成功");
    }
    
    dialogVisible.value = false;
    fetchTasks();
  } catch (error: any) {
    ElMessage.error(error.response?.data?.error || "保存失败");
  } finally {
    submitLoading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    sourceConnectionId: "",
    targetConnectionId: "",
    sourceSchemaName: "",
    targetSchemaName: "",
    tables: [],
    truncateBeforeSync: false,
    status: 'PENDING'
  });
  sourceTableList.value = [];
  formRef.value?.clearValidate();
};

// 对话框关闭处理
const handleDialogClose = () => {
  resetForm();
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks();
  fetchConnections();
  startProgressPolling();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopProgressPolling();
});
</script>

<style scoped>
.main {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.log-container {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 10px;
}

.log-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
