# Test configuration for PostgreSQL cluster synchronization
# This configuration is used for testing the cluster-aware fixes

# Enable debug logging for testing
logging.level.com.dbsync.dbsync=DEBUG
logging.level.com.dbsync.dbsync.service.DatabaseSyncService=DEBUG
logging.level.com.dbsync.dbsync.mapper=DEBUG

# Test database connections for PostgreSQL cluster
db.connections.pgClusterNode1.url=***************************************************
db.connections.pgClusterNode1.username=cqdm_basic
db.connections.pgClusterNode1.password=cqdm_basic_1qaz
db.connections.pgClusterNode1.driverClassName=org.postgresql.Driver
db.connections.pgClusterNode1.dbType=postgresql

# Alternative connection for testing failover
db.connections.pgClusterNode2.url=***************************************************
db.connections.pgClusterNode2.username=cqdm_basic
db.connections.pgClusterNode2.password=cqdm_basic_1qaz
db.connections.pgClusterNode2.driverClassName=org.postgresql.Driver
db.connections.pgClusterNode2.dbType=postgresql

# Oracle source for testing
db.connections.oracleSource.url=*******************************************
db.connections.oracleSource.username=PT1_ECI_CQDM
db.connections.oracleSource.password=ecidh.com2024
db.connections.oracleSource.driverClassName=oracle.jdbc.driver.OracleDriver
db.connections.oracleSource.dbType=oracle

# Test synchronization task for cluster testing
sync.tasks[0].name=OracleToPostgreSQLClusterTest
sync.tasks[0].sourceConnectionId=oracleSource
sync.tasks[0].targetConnectionId=pgClusterNode1
sync.tasks[0].sourceSchemaName=PT1_ECI_CQDM
sync.tasks[0].targetSchemaName=public
sync.tasks[0].tables=BMC_BD_FIX_SOURCE
sync.tasks[0].truncateBeforeSync=true

# Cluster-specific settings
dbsync.cluster.enabled=true
dbsync.cluster.replication-wait-timeout=30000
dbsync.cluster.replication-check-interval=1000
dbsync.cluster.max-retry-attempts=10

# Transaction settings for DDL operations
dbsync.ddl.transaction-timeout=60000
dbsync.ddl.auto-commit=false

# Batch processing settings
dbsync.batch.size=1000
dbsync.batch.timeout=30000

# Connection pool settings for cluster
spring.datasource.postgres.hikari.maximum-pool-size=10
spring.datasource.postgres.hikari.minimum-idle=2
spring.datasource.postgres.hikari.connection-timeout=30000
spring.datasource.postgres.hikari.idle-timeout=600000
spring.datasource.postgres.hikari.max-lifetime=1800000
spring.datasource.postgres.hikari.leak-detection-threshold=60000

# Test-specific MyBatis settings
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.configuration.log-impl=org.apache.ibatis.logging.slf4j.Slf4jImpl

# Enable transaction management
spring.transaction.default-timeout=300
spring.transaction.rollback-on-commit-failure=true
